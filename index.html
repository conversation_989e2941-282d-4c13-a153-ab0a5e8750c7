<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GFX Booster</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🚀</div>
            <h2>GFX Booster</h2>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container">
        <!-- Header -->
        <header class="header">
            <h1 class="app-title">GFX Booster</h1>
            <button class="settings-btn">⚙️</button>
        </header>

        <!-- Home Screen -->
        <div id="home-screen" class="screen active">
            <!-- Device Status Card -->
            <div class="card device-status-card">
                <h3 class="card-title">Device Status</h3>
                <div class="status-metrics">
                    <div class="metric">
                        <span class="metric-icon">🔋</span>
                        <span class="metric-value">78%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-icon">🌡️</span>
                        <span class="metric-value">45°C</span>
                    </div>
                    <div class="metric">
                        <span class="metric-icon">🧠</span>
                        <span class="metric-value">62%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-icon">📶</span>
                        <span class="metric-value">Wi-Fi</span>
                    </div>
                </div>
            </div>

            <!-- Device Info Card -->
            <div class="card device-info-card">
                <h3 class="card-title">Device Information</h3>
                <div class="device-specs">
                    <div class="spec-item">
                        <span class="spec-label">Model:</span>
                        <span class="spec-value">POCO X3</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">CPU:</span>
                        <span class="spec-value">Snapdragon 732G</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution:</span>
                        <span class="spec-value">2400x1080</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Android:</span>
                        <span class="spec-value">Android 13</span>
                    </div>
                </div>
            </div>

            <!-- Game Booster Slider -->
            <div class="card games-card">
                <h3 class="card-title">Game Booster</h3>
                <div class="games-slider">
                    <div class="game-item">
                        <div class="game-icon">🎮</div>
                        <div class="game-name">PUBG Mobile</div>
                        <button class="boost-btn">🚀 Boost</button>
                    </div>
                    <div class="game-item">
                        <div class="game-icon">⚔️</div>
                        <div class="game-name">Call of Duty</div>
                        <button class="boost-btn">🚀 Boost</button>
                    </div>
                    <div class="game-item">
                        <div class="game-icon">🏎️</div>
                        <div class="game-name">Asphalt 9</div>
                        <button class="boost-btn">🚀 Boost</button>
                    </div>
                    <div class="game-item add-game">
                        <div class="add-icon">➕</div>
                        <div class="game-name">Add Game</div>
                    </div>
                </div>
            </div>

            <!-- Boost All Button -->
            <button class="boost-all-btn">🚀 BOOST ALL GAMES</button>
        </div>

        <!-- Tools Screen -->
        <div id="tools-screen" class="screen">
            <div class="tools-grid">
                <div class="tool-card">
                    <div class="tool-icon">📱</div>
                    <h4 class="tool-name">Resolution</h4>
                    <p class="tool-desc">Optimize display resolution</p>
                    <button class="tool-btn">Configure</button>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">📊</div>
                    <h4 class="tool-name">FPS Counter</h4>
                    <p class="tool-desc">Show real-time FPS overlay</p>
                    <button class="tool-btn toggle-fps">Toggle</button>
                </div>
                <div class="tool-card pro">
                    <div class="tool-icon">🎯</div>
                    <h4 class="tool-name">Sensitivity</h4>
                    <p class="tool-desc">Fine-tune touch sensitivity</p>
                    <span class="pro-badge">PRO</span>
                    <button class="tool-btn pro-tool">Unlock</button>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">🔧</div>
                    <h4 class="tool-name">GPU Tuner</h4>
                    <p class="tool-desc">Optimize graphics performance</p>
                    <button class="tool-btn">Tune</button>
                </div>
                <div class="tool-card pro">
                    <div class="tool-icon">🎨</div>
                    <h4 class="tool-name">Graphics</h4>
                    <p class="tool-desc">Advanced graphics settings</p>
                    <span class="pro-badge">PRO</span>
                    <button class="tool-btn pro-tool">Unlock</button>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">🧹</div>
                    <h4 class="tool-name">RAM Cleaner</h4>
                    <p class="tool-desc">Free up memory space</p>
                    <button class="tool-btn">Clean</button>
                </div>
                <div class="tool-card pro">
                    <div class="tool-icon">⚡</div>
                    <h4 class="tool-name">Overclock</h4>
                    <p class="tool-desc">Boost CPU performance</p>
                    <span class="pro-badge">PRO</span>
                    <button class="tool-btn pro-tool">Unlock</button>
                </div>
                <div class="tool-card">
                    <div class="tool-icon">🌡️</div>
                    <h4 class="tool-name">Thermal</h4>
                    <p class="tool-desc">Monitor device temperature</p>
                    <button class="tool-btn">Monitor</button>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <button class="nav-btn active" data-screen="home">
                <span class="nav-icon">🏠</span>
                <span class="nav-label">Home</span>
            </button>
            <button class="nav-btn" data-screen="tools">
                <span class="nav-icon">🧰</span>
                <span class="nav-label">Tools</span>
            </button>
        </nav>
    </div>

    <!-- FPS Overlay -->
    <div id="fps-overlay" class="fps-overlay">
        <span id="fps-counter">60 FPS</span>
    </div>

    <!-- Pro Modal -->
    <div id="pro-modal" class="modal">
        <div class="modal-content">
            <h3>🎁 Watch Ad to Unlock</h3>
            <p>Watch a short video ad to unlock this PRO feature for 24 hours!</p>
            <div class="modal-buttons">
                <button class="modal-btn watch-ad">📺 Watch Ad</button>
                <button class="modal-btn cancel">Cancel</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
