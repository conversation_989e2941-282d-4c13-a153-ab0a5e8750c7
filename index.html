<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GFX Booster</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🚀</div>
            <h2>GFX Booster</h2>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container">
        <!-- Header -->
        <header class="header">
            <button class="back-btn" id="back-btn" style="display: none;">←</button>
            <h1 class="app-title" id="screen-title">GAMING BOOSTER</h1>
            <div class="header-controls">
                <button class="dark-mode-toggle" title="Toggle Dark Mode">🌙</button>
                <button class="settings-btn" title="Settings">⚙️</button>
            </div>
        </header>

        <!-- Home Screen -->
        <div id="home-screen" class="screen active">
            <!-- Description -->
            <div class="description-text">
                <p>Monitor your gaming sessions by tracking background apps and system resource usage for better gaming awareness.</p>
            </div>

            <!-- CPU Info Card -->
            <div class="card info-card">
                <div class="card-header">
                    <div class="card-icon">⚙️</div>
                    <h3 class="card-title">CPU INFO</h3>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Processor</span>
                        <span class="info-value">ums9230_1ette</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Cores</span>
                        <span class="info-value">8 Cores</span>
                    </div>
                </div>
            </div>

            <!-- Memory Info Card -->
            <div class="card info-card">
                <div class="card-header">
                    <div class="card-icon">🧠</div>
                    <h3 class="card-title">MEMORY INFO</h3>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">RAM Type</span>
                        <span class="info-value">LPDDR4</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">TOTAL MEMORY</span>
                        <span class="info-value">6.1 GB</span>
                    </div>
                </div>
            </div>

            <!-- Resource Heavy Apps -->
            <div class="card info-card">
                <div class="card-header">
                    <div class="card-icon">📱</div>
                    <h3 class="card-title">RESOURCE HEAVY APPS</h3>
                </div>
                <p class="resource-desc">These apps are currently using system resources:</p>
                <div class="app-list">
                    <div class="app-item">
                        <div class="app-icon">📱</div>
                        <span class="app-name">System UI</span>
                        <span class="app-usage">45%</span>
                    </div>
                    <div class="app-item">
                        <div class="app-icon">🎮</div>
                        <span class="app-name">Game Launcher</span>
                        <span class="app-usage">23%</span>
                    </div>
                    <div class="app-item">
                        <div class="app-icon">🌐</div>
                        <span class="app-name">Chrome Browser</span>
                        <span class="app-usage">18%</span>
                    </div>
                </div>
            </div>

            <!-- Device Info Card -->
            <div class="card device-info-card">
                <h3 class="card-title">Device Information</h3>
                <div class="device-specs">
                    <div class="spec-item">
                        <div class="spec-icon">📱</div>
                        <div class="spec-content">
                            <span class="spec-label">Device Model</span>
                            <span class="spec-value">POCO X3</span>
                        </div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-icon">⚙️</div>
                        <div class="spec-content">
                            <span class="spec-label">CPU</span>
                            <span class="spec-value">Snapdragon 732G</span>
                        </div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-icon">🖥️</div>
                        <div class="spec-content">
                            <span class="spec-label">Resolution</span>
                            <span class="spec-value">2400x1080</span>
                        </div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-icon">🤖</div>
                        <div class="spec-content">
                            <span class="spec-label">Android Version</span>
                            <span class="spec-value">13</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Booster Section -->
            <div class="card games-card">
                <h3 class="card-title">Game Booster</h3>
                <div class="games-slider">
                    <div class="game-card">
                        <div class="game-image">🎮</div>
                        <div class="game-title">PUBG Mobile</div>
                        <button class="boost-btn">🚀 BOOST</button>
                    </div>
                    <div class="game-card">
                        <div class="game-image">⚔️</div>
                        <div class="game-title">Call of Duty</div>
                        <button class="boost-btn">🚀 BOOST</button>
                    </div>
                    <div class="game-card">
                        <div class="game-image">🏎️</div>
                        <div class="game-title">Asphalt 9</div>
                        <button class="boost-btn">🚀 BOOST</button>
                    </div>
                    <div class="game-card">
                        <div class="game-image">🔫</div>
                        <div class="game-title">Free Fire</div>
                        <button class="boost-btn">🚀 BOOST</button>
                    </div>
                    <div class="game-card add-game-card">
                        <div class="add-game-icon">➕</div>
                        <div class="game-title">Add Game</div>
                    </div>
                </div>
            </div>

            <!-- Boost All Button -->
            <button class="boost-all-btn">🚀 BOOST ALL GAMES</button>
        </div>

        <!-- Gaming Mode Screen -->
        <div id="gaming-mode-screen" class="screen">
            <div class="gaming-mode-container">
                <!-- Gaming Mode Toggle -->
                <div class="gaming-mode-toggle">
                    <div class="toggle-circle" id="gaming-toggle">
                        <div class="toggle-icon">🔥</div>
                    </div>
                    <div class="toggle-text">STOP</div>
                </div>

                <p class="gaming-status">Gaming Session Dashboard Active - System monitoring enabled</p>

                <!-- Device Status -->
                <div class="card device-status-gaming">
                    <h3 class="card-title">Device Status</h3>
                    <div class="status-items">
                        <div class="status-item">
                            <div class="status-icon">🌡️</div>
                            <div class="status-content">
                                <span class="status-label">Battery Temperature</span>
                                <span class="status-value">37.1°C</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon">📶</div>
                            <div class="status-content">
                                <span class="status-label">Network Ping</span>
                                <span class="status-value">49 ms</span>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon">📡</div>
                            <div class="status-content">
                                <span class="status-label">Network Type</span>
                                <span class="status-value">4G</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Game Selection -->
                <div class="card game-selection">
                    <h3 class="card-title">Select Your Favorite Game</h3>
                    <button class="add-game-btn">ADD GAME</button>
                    <p class="pro-text">Pro version: Unlimited games</p>
                </div>
            </div>
        </div>

        <!-- Headshot Tool Screen -->
        <div id="headshot-screen" class="screen">
            <div class="headshot-container">
                <div class="headshot-header">
                    <h2>HEADSHOT TOOL</h2>
                    <p class="headshot-subtitle">GENERAL OPTIMIZATION SETTINGS FOR FREE FIRE</p>
                </div>

                <!-- Device Analysis -->
                <div class="card device-analysis">
                    <h3 class="card-title">DEVICE ANALYSIS</h3>
                    <div class="analysis-list">
                        <div class="analysis-item">
                            <span class="analysis-label">Device:</span>
                            <span class="analysis-value">realme RMX3939</span>
                        </div>
                        <div class="analysis-item">
                            <span class="analysis-label">RAM:</span>
                            <span class="analysis-value">6 GB</span>
                        </div>
                        <div class="analysis-item">
                            <span class="analysis-label">Processor:</span>
                            <span class="analysis-value">ums9230_1ette</span>
                        </div>
                        <div class="analysis-item">
                            <span class="analysis-label">Android:</span>
                            <span class="analysis-value">14</span>
                        </div>
                        <div class="analysis-item">
                            <span class="analysis-label">Screen:</span>
                            <span class="analysis-value">720x1488 px</span>
                        </div>
                        <div class="analysis-item">
                            <span class="analysis-label">DPI:</span>
                            <span class="analysis-value">320</span>
                        </div>
                        <div class="analysis-item">
                            <span class="analysis-label">Approximate FPS estimation:</span>
                            <span class="analysis-value">60</span>
                        </div>
                    </div>
                </div>

                <!-- General Settings -->
                <div class="card general-settings">
                    <h3 class="card-title">GENERAL</h3>
                    <div class="sensitivity-setting">
                        <label class="setting-label">General Sensitivity</label>
                        <div class="slider-container">
                            <input type="range" class="sensitivity-slider" min="0" max="300" value="200">
                            <span class="slider-value">200</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="suggest-btn">SUGGEST BEST SETTINGS</button>
                    <div class="success-message">
                        <span class="success-icon">🎮</span>
                        <span>Settings applied successfully!</span>
                    </div>
                    <button class="crosshair-btn">AIM CROSSHAIR</button>
                </div>
            </div>
        </div>

        <!-- Aim Settings Pro Screen -->
        <div id="aim-settings-screen" class="screen">
            <div class="aim-settings-container">
                <div class="premium-badge">PREMIUM</div>

                <div class="aim-header">
                    <h2>Aim Settings Pro</h2>
                    <p class="aim-subtitle">Aim Crosshair</p>
                </div>

                <!-- Toggle Overlay -->
                <div class="card setting-card">
                    <h3 class="setting-title">Toggle Overlay</h3>
                    <button class="toggle-overlay-btn">OVERLAY ON</button>
                </div>

                <!-- Crosshair Size -->
                <div class="card setting-card">
                    <h3 class="setting-title">Crosshair Size</h3>
                    <div class="slider-container">
                        <input type="range" class="crosshair-slider" min="10" max="100" value="50">
                        <span class="slider-value">50</span>
                    </div>
                </div>

                <!-- Overlay Color -->
                <div class="card setting-card">
                    <h3 class="setting-title">Overlay Color</h3>
                    <div class="color-options">
                        <div class="color-option red active"></div>
                        <div class="color-option green"></div>
                        <div class="color-option blue"></div>
                        <div class="color-option yellow"></div>
                    </div>
                </div>

                <!-- Game Optimization -->
                <div class="card setting-card">
                    <h3 class="setting-title">Game Optimization</h3>
                    <div class="optimization-buttons">
                        <button class="opt-btn">GRAPHICS</button>
                        <button class="opt-btn">AUDIO</button>
                        <button class="opt-btn">PING BOOSTER</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <button class="nav-btn active" data-screen="home">
                <span class="nav-icon">📊</span>
                <span class="nav-label">Monitor</span>
            </button>
            <button class="nav-btn" data-screen="gaming-mode">
                <span class="nav-icon">🎮</span>
                <span class="nav-label">Gaming</span>
            </button>
            <button class="nav-btn" data-screen="headshot">
                <span class="nav-icon">🎯</span>
                <span class="nav-label">Headshot</span>
            </button>
            <button class="nav-btn" data-screen="aim-settings">
                <span class="nav-icon">⚙️</span>
                <span class="nav-label">Aim Pro</span>
            </button>
        </nav>
    </div>

    <!-- FPS Overlay -->
    <div id="fps-overlay" class="fps-overlay">
        <span id="fps-counter">60 FPS</span>
    </div>

    <!-- Ad Banner -->
    <div id="ad-banner" class="ad-banner">
        <div class="ad-content">
            <span class="ad-label">Ad</span>
            <span class="ad-text">🎮 Download Epic Games - Play Now!</span>
            <button class="ad-close">✕</button>
        </div>
    </div>

    <!-- Pro Modal -->
    <div id="pro-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🎁 Watch Ad to Unlock</h3>
                <button class="modal-close">✕</button>
            </div>
            <p>Watch a short video ad to unlock this PRO feature for 24 hours!</p>
            <div class="modal-buttons">
                <button class="modal-btn watch-ad">📺 Watch Ad (30s)</button>
                <button class="modal-btn cancel">Maybe Later</button>
            </div>
        </div>
    </div>

    <!-- Interstitial Ad Modal -->
    <div id="interstitial-modal" class="modal">
        <div class="interstitial-content">
            <div class="ad-video">
                <div class="video-placeholder">
                    <div class="play-icon">▶️</div>
                    <p>Advertisement</p>
                </div>
                <div class="ad-timer">Skip in <span id="skip-timer">5</span>s</div>
            </div>
            <button class="skip-ad" id="skip-ad" style="display: none;">Skip Ad</button>
        </div>
    </div>

    <!-- Loading Transition -->
    <div id="loading-transition" class="loading-transition">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">Loading...</p>
            <p class="loading-tip" id="loading-tip">Tip: Close background apps for better performance</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
