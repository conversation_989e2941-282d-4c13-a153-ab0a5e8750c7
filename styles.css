/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --card-bg: rgba(255, 255, 255, 0.05);
    --neon-blue: #00d4ff;
    --neon-green: #00ff88;
    --neon-purple: #8b5cf6;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --border-color: rgba(255, 255, 255, 0.1);
}

body {
    font-family: 'Poppins', '<PERSON><PERSON>wal', sans-serif;
    background: linear-gradient(135deg, var(--primary-bg) 0%, #1a1a2e 50%, #16213e 100%);
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

body.rtl {
    direction: rtl;
    font-family: 'Tajawal', 'Poppins', sans-serif;
}

/* Light Mode */
body.light-mode {
    --primary-bg: #f5f5f5;
    --secondary-bg: #ffffff;
    --card-bg: rgba(255, 255, 255, 0.8);
    --neon-blue: #0066cc;
    --neon-green: #00aa44;
    --neon-purple: #6b46c1;
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --border-color: rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 100%);
}

body.light-mode .loading-screen {
    background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
}

body.light-mode .header {
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid var(--border-color);
}

body.light-mode .card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body.light-mode .card:hover {
    box-shadow: 0 8px 25px rgba(0, 102, 204, 0.2);
}

body.light-mode .metric-square {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--border-color);
}

body.light-mode .metric-square:hover {
    border-color: var(--neon-blue);
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.2);
}

body.light-mode .spec-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--border-color);
}

body.light-mode .spec-item:hover {
    border-color: var(--neon-green);
}

body.light-mode .game-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--border-color);
}

body.light-mode .game-card:hover {
    border-color: var(--neon-blue);
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.2);
}

body.light-mode .game-image {
    background: rgba(240, 240, 240, 0.9);
    border: 1px solid var(--border-color);
}

body.light-mode .tool-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--border-color);
}

body.light-mode .tool-card:hover {
    border-color: var(--neon-blue);
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.2);
}

body.light-mode .tool-icon {
    background: rgba(240, 240, 240, 0.9);
    border: 1px solid var(--border-color);
}

body.light-mode .bottom-nav {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid var(--border-color);
}

body.light-mode .modal {
    background: rgba(0, 0, 0, 0.5);
}

body.light-mode .modal-content {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

body.light-mode .interstitial-content {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
}

body.light-mode .loading-transition {
    background: var(--primary-bg);
}

body.light-mode .fps-overlay {
    background: rgba(255, 255, 255, 0.9);
    color: var(--neon-green);
    border: 1px solid var(--neon-green);
}

body.light-mode .add-game-card {
    border: 2px dashed var(--border-color);
    background: transparent;
}

body.light-mode .add-game-card:hover {
    border-color: var(--neon-purple);
    background: rgba(107, 70, 193, 0.1);
}

body.light-mode .add-game-icon {
    color: var(--text-secondary);
    border: 1px dashed var(--border-color);
}

/* Smooth theme transition */
body {
    transition: background 0.3s ease, color 0.3s ease;
}

.card,
.metric-square,
.spec-item,
.game-card,
.tool-card,
.game-image,
.tool-icon {
    transition: all 0.3s ease;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 255, 255, 0.3);
    border-top: 3px solid #00ffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* App Container */
.app-container {
    max-width: 768px;
    margin: 0 auto;
    min-height: 100vh;
    padding-bottom: 80px;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.app-container.loaded {
    opacity: 1;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.app-title {
    font-size: 1.3rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
}

.back-btn {
    background: none;
    border: none;
    color: var(--neon-blue);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: var(--card-bg);
    transform: scale(1.1);
}

.header-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.dark-mode-toggle,
.settings-btn {
    background: none;
    border: none;
    font-size: 1.3rem;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode-toggle:hover,
.settings-btn:hover {
    background: var(--card-bg);
    transform: scale(1.1);
}

.settings-btn:hover {
    transform: rotate(90deg) scale(1.1);
}

/* Screen Management */
.screen {
    display: none;
    padding: 1rem 1.5rem;
    animation: fadeIn 0.3s ease;
}

.screen.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--neon-blue);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Description Text */
.description-text {
    padding: 1rem 0;
    text-align: center;
    margin-bottom: 1rem;
}

.description-text p {
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 0.9rem;
}

/* Info Cards */
.info-card {
    margin-bottom: 1rem;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.card-icon {
    font-size: 1.2rem;
    color: var(--neon-blue);
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Resource Apps */
.resource-desc {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.app-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.app-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.app-icon {
    font-size: 1.2rem;
    width: 30px;
    text-align: center;
}

.app-name {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
}

.app-usage {
    color: var(--neon-green);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Device Status */
.status-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.metric-square {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    min-height: 100px;
    justify-content: center;
}

.metric-square:hover {
    border-color: var(--neon-blue);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.metric-icon {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.metric-value {
    font-weight: 600;
    color: var(--neon-green);
    font-size: 1rem;
}

/* Device Info */
.device-specs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.spec-item:hover {
    border-color: var(--neon-green);
    transform: translateX(5px);
}

.spec-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.spec-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.spec-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.spec-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
}

/* Games Slider */
.games-slider {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.games-slider::-webkit-scrollbar {
    display: none;
}

.game-card {
    min-width: 150px;
    text-align: center;
    padding: 1.5rem 1rem;
    background: var(--card-bg);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.game-card:hover {
    transform: scale(1.05);
    border-color: var(--neon-blue);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.game-image {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.game-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
}

.boost-btn {
    background: linear-gradient(45deg, var(--neon-green), var(--neon-blue));
    border: none;
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.boost-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
}

.add-game-card {
    border: 2px dashed var(--border-color);
    cursor: pointer;
    background: transparent;
}

.add-game-card:hover {
    border-color: var(--neon-purple);
    background: rgba(139, 92, 246, 0.1);
}

.add-game-icon {
    font-size: 2.5rem;
    color: var(--text-secondary);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    border: 1px dashed var(--border-color);
}

/* Boost All Button */
.boost-all-btn {
    width: 100%;
    padding: 1.2rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1.5rem;
    position: relative;
    overflow: hidden;
}

.boost-all-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.boost-all-btn:hover::before {
    left: 100%;
}

.boost-all-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 35px rgba(0, 212, 255, 0.4);
}

/* Tools List */
.tools-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tool-card {
    position: relative;
    background: var(--card-bg);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-card:hover {
    transform: translateY(-2px);
    border-color: var(--neon-blue);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.tool-card.pro-tool {
    border-color: rgba(255, 107, 107, 0.5);
}

.tool-card.pro-tool:hover {
    border-color: #ff6b6b;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.2);
}

.tool-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    flex-shrink: 0;
}

.tool-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tool-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.tool-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

.tool-action-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
    border: none;
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
    flex-shrink: 0;
}

.tool-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.pro-unlock {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
}

.pro-unlock:hover {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.pro-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Gaming Mode Screen */
.gaming-mode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    padding-top: 2rem;
}

.gaming-mode-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.toggle-circle {
    width: 150px;
    height: 150px;
    border: 3px solid #ff6b6b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.toggle-circle.active {
    border-color: var(--neon-green);
    background: rgba(0, 255, 136, 0.1);
}

.toggle-icon {
    font-size: 2.5rem;
}

.toggle-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b6b;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.toggle-text.active {
    color: var(--neon-green);
}

.gaming-status {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    max-width: 300px;
    line-height: 1.4;
}

.device-status-gaming {
    width: 100%;
}

.status-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.status-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.status-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.status-value {
    color: var(--neon-green);
    font-weight: 600;
}

.game-selection {
    width: 100%;
    text-align: center;
}

.add-game-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.add-game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.pro-text {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Headshot Screen */
.headshot-container {
    padding-top: 1rem;
}

.headshot-header {
    text-align: center;
    margin-bottom: 2rem;
}

.headshot-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--neon-blue);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.headshot-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.device-analysis {
    margin-bottom: 1.5rem;
}

.analysis-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.analysis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.analysis-item:last-child {
    border-bottom: none;
}

.analysis-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.analysis-value {
    color: var(--text-primary);
    font-weight: 600;
}

.general-settings {
    margin-bottom: 1.5rem;
}

.sensitivity-setting {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.setting-label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1rem;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sensitivity-slider,
.crosshair-slider {
    flex: 1;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.sensitivity-slider::-webkit-slider-thumb,
.crosshair-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--neon-blue);
    border-radius: 50%;
    cursor: pointer;
}

.slider-value {
    color: var(--neon-blue);
    font-weight: 600;
    min-width: 40px;
    text-align: center;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.suggest-btn,
.crosshair-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
    border: none;
    color: white;
    padding: 1rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.suggest-btn:hover,
.crosshair-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.success-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid var(--neon-green);
    border-radius: 12px;
    color: var(--neon-green);
    font-weight: 600;
}

.success-icon {
    font-size: 1.2rem;
}

/* Aim Settings Screen */
.aim-settings-container {
    padding-top: 1rem;
    position: relative;
}

.premium-badge {
    position: absolute;
    top: -1rem;
    right: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    transform: rotate(-5deg);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.aim-header {
    text-align: center;
    margin-bottom: 2rem;
    margin-top: 2rem;
}

.aim-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--neon-blue);
    margin-bottom: 0.5rem;
}

.aim-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.setting-card {
    margin-bottom: 1.5rem;
}

.setting-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--neon-blue);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.toggle-overlay-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    width: 100%;
}

.toggle-overlay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.color-options {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.color-option.red {
    background: #ff6b6b;
}

.color-option.green {
    background: #51cf66;
}

.color-option.blue {
    background: #339af0;
}

.color-option.yellow {
    background: #ffd43b;
}

.color-option.active {
    border-color: var(--text-primary);
    transform: scale(1.2);
}

.optimization-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.opt-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.opt-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

/* Tools List */
.tools-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 768px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-around;
    padding: 0.75rem 0;
    z-index: 100;
}

.nav-btn {
    background: none;
    border: none;
    color: #666666;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    flex: 1;
}

.nav-btn.active {
    color: var(--neon-blue);
    background: rgba(0, 212, 255, 0.1);
}

.nav-icon {
    font-size: 1.3rem;
}

.nav-label {
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tool Card Styles */
.tool-card {
    position: relative;
    background: var(--card-bg);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-card:hover {
    transform: translateY(-2px);
    border-color: var(--neon-blue);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.tool-card.pro-tool {
    border-color: rgba(255, 107, 107, 0.5);
}

.tool-card.pro-tool:hover {
    border-color: #ff6b6b;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.2);
}

.tool-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    flex-shrink: 0;
}

.tool-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tool-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.tool-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

.tool-action-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-green));
    border: none;
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
    flex-shrink: 0;
}

.tool-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.pro-unlock {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
}

.pro-unlock:hover {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.pro-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* FPS Overlay */
.fps-overlay {
    position: fixed;
    top: 100px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff88;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    border: 1px solid #00ff88;
    display: none;
    z-index: 200;
}

.fps-overlay.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* Ad Banner */
.ad-banner {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 768px;
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    padding: 0.75rem 1rem;
    z-index: 50;
    display: none;
}

.ad-banner.active {
    display: block;
    animation: slideUp 0.3s ease;
}

.ad-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
}

.ad-label {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

.ad-text {
    flex: 1;
    text-align: center;
    font-weight: 500;
    font-size: 0.9rem;
}

.ad-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.ad-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--secondary-bg);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid var(--border-color);
    max-width: 350px;
    margin: 0 1rem;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.modal-header h3 {
    margin: 0;
    color: var(--neon-blue);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--card-bg);
    color: var(--text-primary);
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

.modal-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.watch-ad {
    background: linear-gradient(45deg, var(--neon-green), var(--neon-blue));
    color: white;
}

.cancel {
    background: var(--card-bg);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.modal-btn:hover {
    transform: translateY(-2px);
}

.watch-ad:hover {
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
}

/* Interstitial Ad */
.interstitial-content {
    background: var(--primary-bg);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid var(--border-color);
    max-width: 400px;
    margin: 0 1rem;
}

.ad-video {
    position: relative;
    background: #000;
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.video-placeholder {
    aspect-ratio: 16/9;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
    color: var(--text-secondary);
}

.play-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.ad-timer {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.skip-ad {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.skip-ad:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

/* Loading Transition */
.loading-transition {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-bg);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    flex-direction: column;
}

.loading-transition.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.loading-transition .loading-content {
    text-align: center;
}

.loading-transition .loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 212, 255, 0.3);
    border-top: 3px solid var(--neon-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.loading-tip {
    font-size: 0.9rem;
    color: var(--text-secondary);
    max-width: 300px;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 480px) {
    .header {
        padding: 1rem;
    }

    .screen {
        padding: 1rem;
    }

    .status-metrics {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metric-square {
        min-height: 80px;
        padding: 0.75rem;
    }

    .game-card {
        min-width: 130px;
        padding: 1rem 0.75rem;
    }

    .game-image {
        width: 50px;
        height: 50px;
        font-size: 2rem;
    }

    .tool-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .tool-content {
        align-items: center;
        text-align: center;
    }

    .tool-action-btn {
        width: 100%;
    }

    .modal-content {
        padding: 1.5rem;
        max-width: 300px;
    }
}

@media (max-width: 320px) {
    .app-title {
        font-size: 1.3rem;
    }

    .header-controls {
        gap: 0.25rem;
    }

    .dark-mode-toggle,
    .settings-btn {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }

    .status-metrics {
        grid-template-columns: 1fr;
    }

    .game-card {
        min-width: 110px;
    }

    .modal-buttons {
        flex-direction: column;
    }

    .interstitial-content {
        padding: 1.5rem;
        max-width: 280px;
    }
}

/* RTL Support */
.rtl .header-controls {
    flex-direction: row-reverse;
}

.rtl .tool-card {
    direction: rtl;
}

.rtl .spec-item {
    direction: rtl;
}

.rtl .nav-btn {
    direction: rtl;
}

.rtl .pro-badge {
    right: auto;
    left: 1rem;
}
