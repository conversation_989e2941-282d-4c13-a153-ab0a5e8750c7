/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 255, 255, 0.3);
    border-top: 3px solid #00ffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* App Container */
.app-container {
    max-width: 768px;
    margin: 0 auto;
    min-height: 100vh;
    padding-bottom: 80px;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.app-container.loaded {
    opacity: 1;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.settings-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #ffffff;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

/* Screen Management */
.screen {
    display: none;
    padding: 1rem 1.5rem;
    animation: fadeIn 0.3s ease;
}

.screen.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #00ffff;
}

/* Device Status */
.status-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.metric {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.metric-icon {
    font-size: 1.2rem;
}

.metric-value {
    font-weight: 600;
    color: #00ff88;
}

/* Device Info */
.device-specs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    color: #cccccc;
    font-weight: 500;
}

.spec-value {
    color: #ffffff;
    font-weight: 600;
}

/* Games Slider */
.games-slider {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.games-slider::-webkit-scrollbar {
    display: none;
}

.game-item {
    min-width: 140px;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.game-item:hover {
    transform: scale(1.05);
    border-color: #00ffff;
}

.game-icon, .add-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.game-name {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    color: #cccccc;
}

.boost-btn {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.boost-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4);
}

.add-game {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    cursor: pointer;
}

.add-game:hover {
    border-color: #00ffff;
}

/* Boost All Button */
.boost-all-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border: none;
    border-radius: 16px;
    color: white;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.boost-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.4);
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.tool-card {
    position: relative;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
}

.tool-card.pro {
    border-color: #ff6b6b;
}

.tool-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
}

.tool-name {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.tool-desc {
    font-size: 0.8rem;
    color: #cccccc;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.tool-btn {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.tool-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 255, 255, 0.4);
}

.pro-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 768px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-around;
    padding: 1rem 0;
    z-index: 100;
}

.nav-btn {
    background: none;
    border: none;
    color: #666666;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 12px;
}

.nav-btn.active {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
}

.nav-icon {
    font-size: 1.5rem;
}

.nav-label {
    font-size: 0.8rem;
    font-weight: 500;
}

/* FPS Overlay */
.fps-overlay {
    position: fixed;
    top: 100px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff88;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    border: 1px solid #00ff88;
    display: none;
    z-index: 200;
}

.fps-overlay.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 300px;
    margin: 0 1rem;
}

.modal-content h3 {
    margin-bottom: 1rem;
    color: #00ffff;
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: #cccccc;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

.modal-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.watch-ad {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: white;
}

.cancel {
    background: rgba(255, 255, 255, 0.1);
    color: #cccccc;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-btn:hover {
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 480px) {
    .header {
        padding: 1rem;
    }
    
    .screen {
        padding: 1rem;
    }
    
    .status-metrics {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .game-item {
        min-width: 120px;
    }
}

@media (max-width: 320px) {
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
}
