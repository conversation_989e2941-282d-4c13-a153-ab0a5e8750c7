// App State
let currentScreen = 'home';
let fpsOverlayActive = false;
let fpsInterval;

// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const appContainer = document.getElementById('app');
const homeScreen = document.getElementById('home-screen');
const toolsScreen = document.getElementById('tools-screen');
const navButtons = document.querySelectorAll('.nav-btn');
const fpsOverlay = document.getElementById('fps-overlay');
const fpsCounter = document.getElementById('fps-counter');
const proModal = document.getElementById('pro-modal');

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    // Show loading screen for 2 seconds
    setTimeout(() => {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            appContainer.classList.add('loaded');
        }, 500);
    }, 2000);

    initializeEventListeners();
    updateDeviceStatus();
    startFPSCounter();
});

// Event Listeners
function initializeEventListeners() {
    // Bottom Navigation
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const screen = btn.dataset.screen;
            switchScreen(screen);
        });
    });

    // Game Boost Buttons
    document.querySelectorAll('.boost-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            boostGame(btn);
        });
    });

    // Boost All Games Button
    document.querySelector('.boost-all-btn').addEventListener('click', () => {
        boostAllGames();
    });

    // Add Game Button
    document.querySelector('.add-game').addEventListener('click', () => {
        addNewGame();
    });

    // Tool Buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            handleToolClick(btn);
        });
    });

    // FPS Toggle
    document.querySelector('.toggle-fps').addEventListener('click', () => {
        toggleFPSOverlay();
    });

    // Pro Tool Buttons
    document.querySelectorAll('.pro-tool').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            showProModal();
        });
    });

    // Modal Buttons
    document.querySelector('.watch-ad').addEventListener('click', () => {
        watchAd();
    });

    document.querySelector('.cancel').addEventListener('click', () => {
        hideProModal();
    });

    // Close modal on background click
    proModal.addEventListener('click', (e) => {
        if (e.target === proModal) {
            hideProModal();
        }
    });

    // Settings Button
    document.querySelector('.settings-btn').addEventListener('click', () => {
        showNotification('Settings panel coming soon!', 'info');
    });
}

// Screen Management
function switchScreen(screenName) {
    // Update navigation
    navButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.screen === screenName) {
            btn.classList.add('active');
        }
    });

    // Update screens
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });

    if (screenName === 'home') {
        homeScreen.classList.add('active');
    } else if (screenName === 'tools') {
        toolsScreen.classList.add('active');
    }

    currentScreen = screenName;
}

// Game Boost Functions
function boostGame(button) {
    const gameItem = button.closest('.game-item');
    const gameName = gameItem.querySelector('.game-name').textContent;
    
    button.disabled = true;
    button.textContent = '⚡ Boosting...';
    button.style.background = 'linear-gradient(45deg, #00ff88, #00ffff)';

    setTimeout(() => {
        button.textContent = '✅ Boosted';
        showNotification(`${gameName} has been optimized!`, 'success');
        
        setTimeout(() => {
            button.disabled = false;
            button.textContent = '🚀 Boost';
            button.style.background = 'linear-gradient(45deg, #ff6b6b, #ff8e53)';
        }, 3000);
    }, 2000);
}

function boostAllGames() {
    const boostBtn = document.querySelector('.boost-all-btn');
    boostBtn.disabled = true;
    boostBtn.textContent = '⚡ BOOSTING ALL GAMES...';
    boostBtn.style.background = 'linear-gradient(45deg, #00ff88, #00ffff)';

    setTimeout(() => {
        boostBtn.textContent = '✅ ALL GAMES BOOSTED';
        showNotification('All games have been optimized!', 'success');
        
        setTimeout(() => {
            boostBtn.disabled = false;
            boostBtn.textContent = '🚀 BOOST ALL GAMES';
            boostBtn.style.background = 'linear-gradient(45deg, #00ffff, #ff00ff)';
        }, 3000);
    }, 3000);
}

function addNewGame() {
    const gameNames = ['Genshin Impact', 'Free Fire', 'Mobile Legends', 'Clash Royale', 'Among Us'];
    const gameIcons = ['⚔️', '🔫', '🛡️', '👑', '🚀'];
    const randomIndex = Math.floor(Math.random() * gameNames.length);
    
    showNotification(`${gameNames[randomIndex]} added to boost list!`, 'success');
}

// Tool Functions
function handleToolClick(button) {
    const toolCard = button.closest('.tool-card');
    const toolName = toolCard.querySelector('.tool-name').textContent;
    
    if (button.classList.contains('pro-tool')) {
        return; // Handled by pro modal
    }

    button.disabled = true;
    const originalText = button.textContent;
    button.textContent = 'Processing...';

    setTimeout(() => {
        button.disabled = false;
        button.textContent = originalText;
        showNotification(`${toolName} executed successfully!`, 'success');
    }, 1500);
}

function toggleFPSOverlay() {
    fpsOverlayActive = !fpsOverlayActive;
    
    if (fpsOverlayActive) {
        fpsOverlay.classList.add('active');
        showNotification('FPS overlay enabled', 'info');
    } else {
        fpsOverlay.classList.remove('active');
        showNotification('FPS overlay disabled', 'info');
    }
}

// Pro Modal Functions
function showProModal() {
    proModal.classList.add('active');
}

function hideProModal() {
    proModal.classList.remove('active');
}

function watchAd() {
    hideProModal();
    showNotification('🎬 Playing ad...', 'info');
    
    setTimeout(() => {
        showNotification('✅ PRO feature unlocked for 24 hours!', 'success');
    }, 3000);
}

// Device Status Updates
function updateDeviceStatus() {
    setInterval(() => {
        // Update battery (simulate slight changes)
        const batteryElement = document.querySelector('.metric-value');
        let currentBattery = parseInt(batteryElement.textContent);
        currentBattery = Math.max(20, currentBattery - Math.floor(Math.random() * 2));
        batteryElement.textContent = currentBattery + '%';

        // Update temperature
        const tempElement = document.querySelectorAll('.metric-value')[1];
        let currentTemp = parseInt(tempElement.textContent);
        currentTemp = Math.min(65, Math.max(35, currentTemp + (Math.random() - 0.5) * 4));
        tempElement.textContent = Math.round(currentTemp) + '°C';

        // Update RAM usage
        const ramElement = document.querySelectorAll('.metric-value')[2];
        let currentRAM = parseInt(ramElement.textContent);
        currentRAM = Math.min(95, Math.max(30, currentRAM + (Math.random() - 0.5) * 10));
        ramElement.textContent = Math.round(currentRAM) + '%';
    }, 5000);
}

// FPS Counter
function startFPSCounter() {
    let fps = 60;
    fpsInterval = setInterval(() => {
        if (fpsOverlayActive) {
            fps = Math.min(120, Math.max(30, fps + (Math.random() - 0.5) * 10));
            fpsCounter.textContent = Math.round(fps) + ' FPS';
        }
    }, 100);
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Styles for notification
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'rgba(0, 255, 136, 0.9)' : 
                    type === 'error' ? 'rgba(255, 107, 107, 0.9)' : 
                    'rgba(0, 255, 255, 0.9)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        z-index: 1000;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideDown 0.3s ease;
        max-width: 300px;
        text-align: center;
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// Touch and Gesture Support
let touchStartX = 0;
let touchStartY = 0;

document.addEventListener('touchstart', (e) => {
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
});

document.addEventListener('touchend', (e) => {
    if (!touchStartX || !touchStartY) return;

    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    
    const diffX = touchStartX - touchEndX;
    const diffY = touchStartY - touchEndY;

    // Swipe detection for screen switching
    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        if (diffX > 0 && currentScreen === 'home') {
            // Swipe left: Home to Tools
            switchScreen('tools');
        } else if (diffX < 0 && currentScreen === 'tools') {
            // Swipe right: Tools to Home
            switchScreen('home');
        }
    }

    touchStartX = 0;
    touchStartY = 0;
});

// Prevent zoom on double tap
let lastTouchEnd = 0;
document.addEventListener('touchend', (e) => {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        e.preventDefault();
    }
    lastTouchEnd = now;
}, false);
