// App State
let currentScreen = 'home';
let fpsOverlayActive = false;
let fpsInterval;
let adBannerVisible = false;
let interstitialTimer = null;
let lastInterstitialTime = 0;

// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const appContainer = document.getElementById('app');
const homeScreen = document.getElementById('home-screen');
const toolsScreen = document.getElementById('tools-screen');
const navButtons = document.querySelectorAll('.nav-btn');
const fpsOverlay = document.getElementById('fps-overlay');
const fpsCounter = document.getElementById('fps-counter');
const proModal = document.getElementById('pro-modal');
const adBanner = document.getElementById('ad-banner');
const interstitialModal = document.getElementById('interstitial-modal');
const loadingTransition = document.getElementById('loading-transition');
const loadingTip = document.getElementById('loading-tip');
const screenTitle = document.getElementById('screen-title');
const backBtn = document.getElementById('back-btn');

// Loading Tips
const loadingTips = [
    "Tip: Close background apps for better performance",
    "Tip: Enable Game Mode for optimal gaming experience",
    "Tip: Keep your device cool during gaming sessions",
    "Tip: Use headphones for better audio experience",
    "Tip: Adjust graphics settings based on your device",
    "Tip: Clear cache regularly to maintain performance",
    "Tip: Update your games for latest optimizations"
];

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    // Load saved theme first
    loadSavedTheme();

    // Show loading screen for 2 seconds
    setTimeout(() => {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            appContainer.classList.add('loaded');
            showAdBanner(); // Show ad banner after loading
        }, 500);
    }, 2000);

    initializeEventListeners();
    updateDeviceStatus();
    startFPSCounter();
    startInterstitialTimer();
});

// Event Listeners
function initializeEventListeners() {
    // Bottom Navigation
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const screen = btn.dataset.screen;
            switchScreen(screen);
        });
    });

    // Game Boost Buttons
    document.querySelectorAll('.boost-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            boostGame(btn);
        });
    });

    // Boost All Games Button
    const boostAllBtn = document.querySelector('.boost-all-btn');
    if (boostAllBtn) {
        boostAllBtn.addEventListener('click', () => {
            boostAllGames();
        });
    }

    // Add Game Button
    const addGameCard = document.querySelector('.add-game-card');
    if (addGameCard) {
        addGameCard.addEventListener('click', () => {
            addNewGame();
        });
    }

    // Tool Action Buttons
    document.querySelectorAll('.tool-action-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            handleToolClick(btn);
        });
    });

    // FPS Toggle
    const fpsToggle = document.querySelector('.toggle-fps');
    if (fpsToggle) {
        fpsToggle.addEventListener('click', () => {
            toggleFPSOverlay();
        });
    }

    // Pro Tool Buttons
    document.querySelectorAll('.pro-unlock').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            showProModal();
        });
    });

    // Modal Buttons
    document.querySelector('.watch-ad').addEventListener('click', () => {
        watchAd();
    });

    document.querySelector('.cancel').addEventListener('click', () => {
        hideProModal();
    });

    // Modal Close Buttons
    document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', () => {
            hideProModal();
        });
    });

    // Close modal on background click
    proModal.addEventListener('click', (e) => {
        if (e.target === proModal) {
            hideProModal();
        }
    });

    // Ad Banner Close
    document.querySelector('.ad-close').addEventListener('click', () => {
        hideAdBanner();
    });

    // Skip Ad Button
    document.getElementById('skip-ad').addEventListener('click', () => {
        hideInterstitialAd();
    });

    // Settings Button
    document.querySelector('.settings-btn').addEventListener('click', () => {
        showNotification('Settings panel coming soon!', 'info');
    });

    // Dark Mode Toggle
    document.querySelector('.dark-mode-toggle').addEventListener('click', () => {
        toggleDarkMode();
    });
}

// Screen Management
function switchScreen(screenName) {
    if (currentScreen === screenName) return;

    // Show loading transition
    showLoadingTransition();

    setTimeout(() => {
        // Update navigation
        navButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.screen === screenName) {
                btn.classList.add('active');
            }
        });

        // Update screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Update header and show appropriate screen
        updateHeader(screenName);

        switch(screenName) {
            case 'home':
                homeScreen.classList.add('active');
                break;
            case 'tools':
                toolsScreen.classList.add('active');
                break;
        }

        currentScreen = screenName;

        // Hide loading transition
        setTimeout(() => {
            hideLoadingTransition();
        }, 500);
    }, 300);
}

// Update Header
function updateHeader(screenName) {
    const titles = {
        'home': 'GFX BOOSTER',
        'tools': 'الأدوات'
    };

    screenTitle.textContent = titles[screenName] || 'GFX BOOSTER';

    // Hide back button for main screens
    backBtn.style.display = 'none';
}

// Game Boost Functions
function boostGame(button) {
    const gameCard = button.closest('.game-card');
    const gameName = gameCard.querySelector('.game-title').textContent;

    button.disabled = true;
    button.textContent = '⚡ BOOSTING...';
    button.style.background = 'linear-gradient(45deg, #00ff88, #00ffff)';

    setTimeout(() => {
        button.textContent = '✅ BOOSTED';
        showNotification(`${gameName} has been optimized!`, 'success');

        setTimeout(() => {
            button.disabled = false;
            button.textContent = '🚀 BOOST';
            button.style.background = '';
        }, 3000);
    }, 2000);
}

function boostAllGames() {
    const boostBtn = document.querySelector('.boost-all-btn');
    boostBtn.disabled = true;
    boostBtn.textContent = '⚡ BOOSTING ALL GAMES...';
    boostBtn.style.background = 'linear-gradient(45deg, #00ff88, #00ffff)';

    setTimeout(() => {
        boostBtn.textContent = '✅ ALL GAMES BOOSTED';
        showNotification('All games have been optimized!', 'success');

        setTimeout(() => {
            boostBtn.disabled = false;
            boostBtn.textContent = '🚀 BOOST ALL GAMES';
            boostBtn.style.background = '';
        }, 3000);
    }, 3000);
}

function addNewGame() {
    const gameNames = ['Free Fire', 'PUBG Mobile', 'Call of Duty', 'Mobile Legends', 'Clash Royale'];
    const randomIndex = Math.floor(Math.random() * gameNames.length);

    showNotification(`${gameNames[randomIndex]} تم إضافته لقائمة الألعاب!`, 'success');
}

function boostGame(button) {
    const gameCard = button.closest('.game-card');
    const gameName = gameCard.querySelector('.game-title').textContent;

    button.disabled = true;
    button.textContent = '⚡ جاري التسريع...';
    button.style.background = 'linear-gradient(45deg, #00ff88, #00ffff)';

    setTimeout(() => {
        button.textContent = '✅ تم التسريع';
        showNotification(`تم تحسين ${gameName} بنجاح!`, 'success');

        setTimeout(() => {
            button.disabled = false;
            button.textContent = '🚀 BOOST';
            button.style.background = '';
        }, 3000);
    }, 2000);
}

function boostAllGames() {
    const boostBtn = document.querySelector('.boost-all-btn');
    boostBtn.disabled = true;
    boostBtn.textContent = '⚡ جاري تسريع جميع الألعاب...';
    boostBtn.style.background = 'linear-gradient(45deg, #00ff88, #00ffff)';

    setTimeout(() => {
        boostBtn.textContent = '✅ تم تسريع جميع الألعاب';
        showNotification('تم تحسين جميع الألعاب بنجاح!', 'success');

        setTimeout(() => {
            boostBtn.disabled = false;
            boostBtn.textContent = '🚀 BOOST ALL GAMES';
            boostBtn.style.background = '';
        }, 3000);
    }, 3000);
}

// Tool Functions
function handleToolClick(button) {
    const toolCard = button.closest('.tool-card');
    const toolName = toolCard.querySelector('.tool-title').textContent;

    if (button.classList.contains('pro-unlock')) {
        return; // Handled by pro modal
    }

    button.disabled = true;
    const originalText = button.textContent;
    button.textContent = 'جاري المعالجة...';

    setTimeout(() => {
        button.disabled = false;
        button.textContent = originalText;
        showNotification(`تم تنفيذ ${toolName} بنجاح!`, 'success');

        // Show interstitial ad occasionally
        if (Math.random() < 0.3) {
            setTimeout(() => showInterstitialAd(), 1000);
        }
    }, 1500);
}

function toggleFPSOverlay() {
    fpsOverlayActive = !fpsOverlayActive;

    if (fpsOverlayActive) {
        fpsOverlay.classList.add('active');
        showNotification('تم تفعيل عداد الإطارات', 'info');
    } else {
        fpsOverlay.classList.remove('active');
        showNotification('تم إلغاء عداد الإطارات', 'info');
    }
}

// Tool Functions
function handleToolClick(button) {
    const toolCard = button.closest('.tool-card');
    const toolName = toolCard.querySelector('.tool-title').textContent;

    if (button.classList.contains('pro-unlock')) {
        return; // Handled by pro modal
    }

    button.disabled = true;
    const originalText = button.textContent;
    button.textContent = 'Processing...';

    setTimeout(() => {
        button.disabled = false;
        button.textContent = originalText;
        showNotification(`${toolName} executed successfully!`, 'success');

        // Show interstitial ad occasionally
        if (Math.random() < 0.3) {
            setTimeout(() => showInterstitialAd(), 1000);
        }
    }, 1500);
}

function toggleFPSOverlay() {
    fpsOverlayActive = !fpsOverlayActive;
    
    if (fpsOverlayActive) {
        fpsOverlay.classList.add('active');
        showNotification('FPS overlay enabled', 'info');
    } else {
        fpsOverlay.classList.remove('active');
        showNotification('FPS overlay disabled', 'info');
    }
}

// Pro Modal Functions
function showProModal() {
    proModal.classList.add('active');
}

function hideProModal() {
    proModal.classList.remove('active');
}

function watchAd() {
    hideProModal();
    showInterstitialAd();
}

// Ad System Functions
function showAdBanner() {
    if (!adBannerVisible) {
        adBanner.classList.add('active');
        adBannerVisible = true;

        // Auto hide after 10 seconds
        setTimeout(() => {
            if (adBannerVisible) {
                hideAdBanner();
            }
        }, 10000);
    }
}

function hideAdBanner() {
    adBanner.classList.remove('active');
    adBannerVisible = false;
}

function showInterstitialAd() {
    interstitialModal.classList.add('active');
    let countdown = 5;
    const skipTimer = document.getElementById('skip-timer');
    const skipButton = document.getElementById('skip-ad');

    skipButton.style.display = 'none';

    const timer = setInterval(() => {
        skipTimer.textContent = countdown;
        countdown--;

        if (countdown < 0) {
            clearInterval(timer);
            skipButton.style.display = 'block';
            skipTimer.parentElement.style.display = 'none';
        }
    }, 1000);
}

function hideInterstitialAd() {
    interstitialModal.classList.remove('active');
    showNotification('✅ PRO feature unlocked for 24 hours!', 'success');

    // Reset timer display
    const skipTimer = document.getElementById('skip-timer');
    const skipButton = document.getElementById('skip-ad');
    skipTimer.parentElement.style.display = 'block';
    skipButton.style.display = 'none';
    skipTimer.textContent = '5';
}

function startInterstitialTimer() {
    // Show interstitial ad every 5 minutes
    setInterval(() => {
        const now = Date.now();
        if (now - lastInterstitialTime > 300000) { // 5 minutes
            if (Math.random() < 0.4) { // 40% chance
                showInterstitialAd();
                lastInterstitialTime = now;
            }
        }
    }, 60000); // Check every minute
}

// Device Status Updates
function updateDeviceStatus() {
    setInterval(() => {
        // Update device metrics
        const metricValues = document.querySelectorAll('.metric-value');

        if (metricValues.length >= 4) {
            // Update battery (decrease slowly)
            let battery = parseInt(metricValues[0].textContent);
            battery = Math.max(20, battery - Math.floor(Math.random() * 2));
            metricValues[0].textContent = battery + '%';

            // Update CPU temperature
            let temp = parseInt(metricValues[1].textContent);
            temp = Math.min(65, Math.max(35, temp + (Math.random() - 0.5) * 4));
            metricValues[1].textContent = Math.round(temp) + '°C';

            // Update RAM usage
            let ram = parseInt(metricValues[2].textContent);
            ram = Math.min(95, Math.max(30, ram + (Math.random() - 0.5) * 10));
            metricValues[2].textContent = Math.round(ram) + '%';
        }
    }, 5000);
}

// FPS Counter
function startFPSCounter() {
    let fps = 60;
    fpsInterval = setInterval(() => {
        if (fpsOverlayActive) {
            fps = Math.min(120, Math.max(30, fps + (Math.random() - 0.5) * 10));
            fpsCounter.textContent = Math.round(fps) + ' FPS';
        }
    }, 100);
}

// Loading Transition Functions
function showLoadingTransition() {
    const randomTip = loadingTips[Math.floor(Math.random() * loadingTips.length)];
    loadingTip.textContent = randomTip;
    loadingTransition.classList.add('active');
}

function hideLoadingTransition() {
    loadingTransition.classList.remove('active');
}

// Dark Mode Toggle
function toggleDarkMode() {
    document.body.classList.toggle('light-mode');
    const isLightMode = document.body.classList.contains('light-mode');
    const toggle = document.querySelector('.dark-mode-toggle');

    if (isLightMode) {
        toggle.textContent = '☀️';
        toggle.title = 'Switch to Dark Mode';
        showNotification('Light mode enabled', 'info');
        localStorage.setItem('theme', 'light');
    } else {
        toggle.textContent = '🌙';
        toggle.title = 'Switch to Light Mode';
        showNotification('Dark mode enabled', 'info');
        localStorage.setItem('theme', 'dark');
    }
}

// Load saved theme on page load
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme');
    const toggle = document.querySelector('.dark-mode-toggle');

    if (savedTheme === 'light') {
        document.body.classList.add('light-mode');
        toggle.textContent = '☀️';
        toggle.title = 'Switch to Dark Mode';
    } else {
        document.body.classList.remove('light-mode');
        toggle.textContent = '🌙';
        toggle.title = 'Switch to Light Mode';
    }
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Styles for notification
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'rgba(0, 255, 136, 0.9)' :
                    type === 'error' ? 'rgba(255, 107, 107, 0.9)' :
                    'rgba(0, 212, 255, 0.9)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        z-index: 1000;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideDown 0.3s ease;
        max-width: 300px;
        text-align: center;
        font-family: 'Poppins', sans-serif;
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideUp 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// Touch and Gesture Support
let touchStartX = 0;
let touchStartY = 0;

document.addEventListener('touchstart', (e) => {
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
});

document.addEventListener('touchend', (e) => {
    if (!touchStartX || !touchStartY) return;

    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    
    const diffX = touchStartX - touchEndX;
    const diffY = touchStartY - touchEndY;

    // Swipe detection for screen switching
    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        if (diffX > 0 && currentScreen === 'home') {
            // Swipe left: Home to Tools
            switchScreen('tools');
        } else if (diffX < 0 && currentScreen === 'tools') {
            // Swipe right: Tools to Home
            switchScreen('home');
        }
    }

    touchStartX = 0;
    touchStartY = 0;
});

// Prevent zoom on double tap
let lastTouchEnd = 0;
document.addEventListener('touchend', (e) => {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        e.preventDefault();
    }
    lastTouchEnd = now;
}, false);
